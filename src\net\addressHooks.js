// 逆地理编码API - 获取详细地址信息
const reverseGeocode = async (longitude, latitude) => {
  const AMAP_KEY = '81ea7b2440e2bddd9f6b540fa04c141d';
  try {
    const url = `https://restapi.amap.com/v3/geocode/regeo?location=${longitude},${latitude}&key=${AMAP_KEY}&extensions=all&radius=1000`;

    console.log('逆地理编码查询:', `${longitude},${latitude}`);
    const response = await fetch(url);
    const data = await response.json();

    if (data.status === '1' && data.regeocode) {
      const regeocode = data.regeocode;
      const addressComponent = regeocode.addressComponent;

      console.log('逆地理编码返回数据:', JSON.stringify(regeocode, null, 2));
      console.log('地址组件详情:', {
        township: addressComponent.township,
        street: addressComponent.street,
        streetNumber: addressComponent.streetNumber,
        neighborhood: addressComponent.neighborhood,
        building: addressComponent.building,
        district: addressComponent.district
      });

      return {
        regeocode: regeocode, // 返回完整的regeocode对象
        street: addressComponent.township || '', // 行政街道
        district: addressComponent.district || '',
        city: addressComponent.city || '长沙市',
        province: addressComponent.province || '湖南省',
        formatted_address: regeocode.formatted_address || '',
        business_areas: regeocode.aois || []
      };
    }
    return null;
  } catch (error) {
    console.error('逆地理编码失败:', error);
    return null;
  }
};

// 从逆地理编码结果中提取行政街道信息
const extractAdministrativeStreet = (regeocode) => {
  if (!regeocode || !regeocode.addressComponent) return '';

  const addressComponent = regeocode.addressComponent;

  // 优先获取township（乡镇街道）
  if (addressComponent.township && addressComponent.township.includes('街道')) {
    return addressComponent.township;
  }

  // 如果township不包含"街道"，检查是否有其他街道信息
  if (addressComponent.township) {
    return addressComponent.township;
  }

  // 检查neighborhood（社区/居委会）是否包含街道信息
  if (addressComponent.neighborhood && addressComponent.neighborhood.includes('街道')) {
    return addressComponent.neighborhood;
  }

  return '';
};

// POI搜索API - 获取建筑物详细信息
const searchPOI = async (buildingName) => {
  const AMAP_KEY = '81ea7b2440e2bddd9f6b540fa04c141d';
  try {
    const url = `https://restapi.amap.com/v3/place/text?keywords=${encodeURIComponent(buildingName)}&city=长沙&key=${AMAP_KEY}&extensions=all`;

    console.log('POI搜索查询:', buildingName);
    const response = await fetch(url);
    const data = await response.json();

    if (data.status === '1' && data.pois && data.pois.length > 0) {
      const poi = data.pois[0]; // 取第一个结果
      const location = poi.location.split(',');

      console.log('POI搜索返回数据:', JSON.stringify(poi, null, 2));

      // 使用逆地理编码获取详细的行政街道信息
      const longitude = parseFloat(location[0]);
      const latitude = parseFloat(location[1]);
      const detailedAddress = await reverseGeocode(longitude, latitude);

      // 提取行政街道信息
      const administrativeStreet = detailedAddress ?
        extractAdministrativeStreet(detailedAddress.regeocode || detailedAddress) : '';

      return {
        longitude: longitude,
        latitude: latitude,
        street: administrativeStreet || '', // 使用行政街道信息
        district: poi.adname || detailedAddress?.district || '', // 行政区名称
        city: poi.cityname || detailedAddress?.city || '长沙市',
        province: poi.pname || detailedAddress?.province || '湖南省',
        formatted_address: poi.address || detailedAddress?.formatted_address || '',
        poi_name: poi.name,
        poi_type: poi.type,
        original_address: poi.address, // 保留原始地址
        administrative_info: detailedAddress // 保留完整的行政信息用于调试
      };
    }
    return null;
  } catch (error) {
    console.error('POI搜索失败:', error);
    return null;
  }
};

// 高德地理编码API - 增强版，包含街道信息和重试机制
export const geocodeBuilding = async (buildingName, address = '', maxRetries = 2) => {
  const AMAP_KEY = '81ea7b2440e2bddd9f6b540fa04c141d';

  // 首先尝试POI搜索，这通常能获取到更准确的建筑物信息
  console.log('尝试POI搜索:', buildingName);
  const poiResult = await searchPOI(buildingName);
  if (poiResult) {
    console.log('POI搜索成功:', poiResult);
    return poiResult;
  }

  // 如果POI搜索失败，回退到地理编码
  console.log('POI搜索失败，回退到地理编码');

  // 定义多种查询策略
  const queryStrategies = [
    address ? `${address} ${buildingName}` : `长沙市 ${buildingName}`,
    `湖南省长沙市 ${buildingName}`,
    `长沙 ${buildingName}`,
    buildingName // 最后尝试只用建筑名称
  ];

  for (let strategyIndex = 0; strategyIndex < queryStrategies.length; strategyIndex++) {
    const query = queryStrategies[strategyIndex];

    for (let retry = 0; retry <= maxRetries; retry++) {
      try {
        const url = `https://restapi.amap.com/v3/geocode/geo?address=${encodeURIComponent(query)}&key=${AMAP_KEY}&extensions=all`;

        console.log(`地理编码查询 (策略${strategyIndex + 1}, 尝试${retry + 1}):`, query);
        const response = await fetch(url);
        const data = await response.json();

        if (data.status === '1' && data.geocodes && data.geocodes.length > 0) {
          const geocode = data.geocodes[0];
          const location = geocode.location.split(',');
          const longitude = parseFloat(location[0]);
          const latitude = parseFloat(location[1]);

          // 详细打印API返回的数据结构，用于调试
          console.log('地理编码API返回的完整数据:', JSON.stringify(geocode, null, 2));

          // 使用逆地理编码获取详细地址信息
          const detailedAddress = await reverseGeocode(longitude, latitude);

          // 提取行政街道信息
          const administrativeStreet = detailedAddress ?
            extractAdministrativeStreet(detailedAddress.regeocode || detailedAddress) : '';

          const result = {
            longitude: longitude,
            latitude: latitude,
            street: administrativeStreet || '', // 使用行政街道信息
            district: detailedAddress?.district || geocode.district || '', // 区县信息
            city: detailedAddress?.city || geocode.city || '长沙市', // 城市信息
            province: detailedAddress?.province || geocode.province || '湖南省', // 省份信息
            formatted_address: detailedAddress?.formatted_address || geocode.formatted_address || '', // 完整地址
            level: geocode.level || '', // 地址精度级别
            query_used: query, // 记录成功的查询策略
            // 添加原始数据用于调试
            raw_data: {
              geocode: {
                street: geocode.street,
                township: geocode.township,
                district: geocode.district,
                city: geocode.city
              },
              reverse_geocode: detailedAddress,
              administrative_street: administrativeStreet
            }
          };

          console.log(`地理编码成功: ${buildingName}`, result);
          return result;
        } else {
          console.warn(`地理编码失败 (策略${strategyIndex + 1}, 尝试${retry + 1}):`, data);

          // 如果不是最后一次重试，等待一段时间再重试
          if (retry < maxRetries) {
            await new Promise(resolve => setTimeout(resolve, 300 * (retry + 1)));
          }
        }
      } catch (error) {
        console.error(`地理编码请求失败 (策略${strategyIndex + 1}, 尝试${retry + 1}):`, error);

        // 如果不是最后一次重试，等待一段时间再重试
        if (retry < maxRetries) {
          await new Promise(resolve => setTimeout(resolve, 300 * (retry + 1)));
        }
      }
    }

    // 策略间的等待时间
    if (strategyIndex < queryStrategies.length - 1) {
      await new Promise(resolve => setTimeout(resolve, 200));
    }
  }

  console.warn(`所有查询策略都失败: ${buildingName}`);
  return null;
};

const addressHooksData = [
  {
    "id": "1947224199273304064",
    "name": "德思勤城市广场",
    "longitude": 113.011364,
    "latitude": 28.111692
  },
  {
    "id": "1947224199273304065",
    "name": "华尔街中心",
    "longitude": 112.986293,
    "latitude": 28.215414
  },
  {
    "id": "1947224199273304066",
    "name": "华远国际中心",
    "longitude": 112.970098,
    "latitude": 28.188992
  },
  {
    "id": "1937377505647960081",
    "name": "汇景发展环球中心",
    "longitude": 112.969396,
    "latitude": 28.174121
  },
  {
    "id": "1947224199273304067",
    "name": "长沙佳兆业广场",
    "longitude": 113.001113,
    "latitude": 28.195141
  },
  {
    "id": "1947224199273304068",
    "name": "长沙绿地中心T1栋",
    "longitude": 112.98737,
    "latitude": 28.211309
  },
  {
    "id": "1947224199273304069",
    "name": "润和金融中心",
    "longitude": 112.962447,
    "latitude": 28.24257
  },
  {
    "id": "1947224199273304070",
    "name": "湖南商会大厦",
    "longitude": 112.986227,
    "latitude": 28.149551
  },
  {
    "id": "1947224199273304071",
    "name": "万坤图财富广场",
    "longitude": 113.030288,
    "latitude": 28.138137
  },
  {
    "id": "1947224199273304072",
    "name": "新湖南大厦",
    "longitude": 112.985416,
    "latitude": 28.207419
  },
  {
    "id": "1947224199273304073",
    "name": "兴业IEC",
    "longitude": 112.986135,
    "latitude": 28.151208
  },
  {
    "id": "1947224199273304074",
    "name": "御邦国际广场",
    "longitude": 112.979791,
    "latitude": 28.106223
  },
  {
    "id": "1947224199273304075",
    "name": "运达中央广场A座",
    "longitude": 113.043613,
    "latitude": 28.169985
  },
  {
    "id": "1947224199273304076",
    "name": "长房国际大厦",
    "longitude": 113.026141,
    "latitude": 28.185087
  },
  {
    "id": "1947224199273304077",
    "name": "中建广场",
    "longitude": 113.006584,
    "latitude": 28.119073
  },
  {
    "id": "1947224199273304078",
    "name": "中国石油长沙大厦",
    "longitude": 112.986413,
    "latitude": 28.186327
  },
  {
    "id": "1947224199273304079",
    "name": "香泽南湖大厦",
    "longitude": 113.010627,
    "latitude": 28.203449
  },
  {
    "id": "1947224199273304080",
    "name": "北辰时代广场A1座",
    "longitude": 112.979943,
    "latitude": 28.238727
  },
  {
    "id": "1947224199273304081",
    "name": "骏达大厦",
    "longitude": 112.844716,
    "latitude": 28.198836
  },
  {
    "id": "1947224199273304082",
    "name": "河西王府井写字楼",
    "longitude": 112.932812,
    "latitude": 28.218383
  },
  {
    "id": "1947224199273304083",
    "name": "红橡国际广场",
    "longitude": 113.039912,
    "latitude": 28.207328
  },
  {
    "id": "1947224199273304084",
    "name": "金茂ICC",
    "longitude": 112.903799,
    "latitude": 28.196713
  },
  {
    "id": "1937377505639571461",
    "name": "长沙西中心",
    "longitude": 112.913188,
    "latitude": 28.209433
  },
  {
    "id": "1937377505647960067",
    "name": "嘉熙中心",
    "longitude": 112.990761,
    "latitude": 28.185284
  },
  {
    "id": "1947224199273304085",
    "name": "保利国际广场",
    "longitude": 112.968695,
    "latitude": 28.166477
  },
  {
    "id": "1947224199273304086",
    "name": "蓝湾国际广场",
    "longitude": 112.97152,
    "latitude": 28.165379
  },
  {
    "id": "1947224199277498416",
    "name": "富兴世界金融中心T2",
    "longitude": 112.988366,
    "latitude": 28.216574
  },
  {
    "id": "1947224199273304088",
    "name": "通程国际大酒店",
    "longitude": 112.997907,
    "latitude": 28.189115
  },
  {
    "id": "1947224199277498368",
    "name": "天城国际广场",
    "longitude": 112.988601,
    "latitude": 28.118955
  },
  {
    "id": "1947224199277498369",
    "name": "友阿总部办公大楼",
    "longitude": 113.007717,
    "latitude": 28.187264
  },
  {
    "id": "1947224199277498370",
    "name": "长房时代国际",
    "longitude": 112.909228,
    "latitude": 28.21884
  },
  {
    "id": "1947224199277498371",
    "name": "华创国际广场",
    "longitude": 112.988469,
    "latitude": 28.22421
  },
  {
    "id": "1937377505639571459",
    "name": "长房时代天地",
    "longitude": 112.909255,
    "latitude": 28.217799
  },
  {
    "id": "1947224199277498372",
    "name": "宇成朝阳广场",
    "longitude": 113.010128,
    "latitude": 28.187118
  },
  {
    "id": "1937112312825147392",
    "name": "长沙国金中心",
    "longitude": 112.978726,
    "latitude": 28.192457
  },
  {
    "id": "1947224199277498373",
    "name": "湘江时代商务广场",
    "longitude": 112.945685,
    "latitude": 28.130471
  },
  {
    "id": "1947224199277498374",
    "name": "楷林国际C座",
    "longitude": 112.962391,
    "latitude": 28.235691
  },
  {
    "id": "1947224199277498375",
    "name": "华雅财富大厦",
    "longitude": 113.030734,
    "latitude": 28.170372
  },
  {
    "id": "1947224199277498376",
    "name": "凯乐·微谷",
    "longitude": 112.994296,
    "latitude": 28.237539
  },
  {
    "id": "1937377505626988544",
    "name": "华泰尚都律政服务大楼",
    "longitude": 113.02463,
    "latitude": 28.212305
  },
  {
    "id": "1947224199277498377",
    "name": "湘江集团大厦",
    "longitude": 112.904381,
    "latitude": 28.195749
  },
  {
    "id": "1947224199277498378",
    "name": "世景国际广场A栋",
    "longitude": 113.09828,
    "latitude": 28.244864
  },
  {
    "id": "1947224199277498379",
    "name": "万达广场写字楼A座",
    "longitude": 112.970997,
    "latitude": 28.19873
  },
  {
    "id": "1947224199277498380",
    "name": "湘江财富金融中心A座",
    "longitude": 112.970522,
    "latitude": 28.199382
  },
  {
    "id": "1947224199277498381",
    "name": "中盈广场",
    "longitude": 112.949188,
    "latitude": 28.124467
  },
  {
    "id": "1947224199277498382",
    "name": "顺天国际金融中心",
    "longitude": 112.995413,
    "latitude": 28.158384
  },
  {
    "id": "1947224199277498383",
    "name": "天健·壹平方英里H栋",
    "longitude": 112.986387,
    "latitude": 28.221965
  },
  {
    "id": "1947224199277498384",
    "name": "旺德府国际大厦",
    "longitude": 113.029717,
    "latitude": 28.185775
  },
  {
    "id": "1947224199277498385",
    "name": "运达国际广场",
    "longitude": 112.985203,
    "latitude": 28.202897
  },
  {
    "id": "1947224199277498386",
    "name": "梅溪湖创新中心",
    "longitude": 112.891404,
    "latitude": 28.19276
  },
  {
    "id": "1947224199277498387",
    "name": "顺天国际财富中心",
    "longitude": 112.986495,
    "latitude": 28.189619
  },
  {
    "id": "1947224199277498388",
    "name": "长沙·视谷中心",
    "longitude": 113.027578,
    "latitude": 28.223332
  },
  {
    "id": "1947224199277498389",
    "name": "柏宁地王广场",
    "longitude": 112.948907,
    "latitude": 28.129461
  },
  {
    "id": "1947224199277498390",
    "name": "新长海广场",
    "longitude": 113.075396,
    "latitude": 28.226685
  },
  {
    "id": "1947224199277498391",
    "name": "泊富国际广场",
    "longitude": 112.984229,
    "latitude": 28.209071
  },
  {
    "id": "1947224199277498392",
    "name": "绿地中央广场",
    "longitude": 112.95124,
    "latitude": 28.227964
  },
  {
    "id": "1947224199277498393",
    "name": "紫鑫中央广场",
    "longitude": 112.828906,
    "latitude": 28.35617
  },
  {
    "id": "1947224199277498394",
    "name": "双塔国际广场",
    "longitude": 113.02449,
    "latitude": 28.111705
  },
  {
    "id": "1947224199277498395",
    "name": "中天广场",
    "longitude": 112.982674,
    "latitude": 28.195657
  },
  {
    "id": "1947224199277498396",
    "name": "汇金国际",
    "longitude": 112.984612,
    "latitude": 28.16267
  },
  {
    "id": "1947224199277498397",
    "name": "喜盈门·范城",
    "longitude": 113.034851,
    "latitude": 28.132362
  },
  {
    "id": "1947224199277498398",
    "name": "华晨·双帆国际",
    "longitude": 113.028727,
    "latitude": 28.162121
  },
  {
    "id": "1947224199277498399",
    "name": "壹号座品",
    "longitude": 112.995721,
    "latitude": 28.197022
  },
  {
    "id": "1947224199277498400",
    "name": "世茂环球金融中心",
    "longitude": 112.984623,
    "latitude": 28.193389
  },
  {
    "id": "1947224199277498401",
    "name": "湖南投资大厦",
    "longitude": 112.984226,
    "latitude": 28.194341
  },
  {
    "id": "1947224199277498402",
    "name": "新时空大厦",
    "longitude": 112.985013,
    "latitude": 28.160616
  },
  {
    "id": "1947224199277498403",
    "name": "复地·星光天地",
    "longitude": 113.006939,
    "latitude": 28.111922
  },
  {
    "id": "1937112312816758784",
    "name": "华润凤凰中心",
    "longitude": 113.112101,
    "latitude": 28.246531
  },
  {
    "id": "1947224199277498404",
    "name": "万家丽国际商务中心",
    "longitude": 113.031879,
    "latitude": 28.191061
  },
  {
    "id": "1947224199277498405",
    "name": "银华大厦",
    "longitude": 112.98905,
    "latitude": 28.195375
  },
  {
    "id": "1947224199277498406",
    "name": "德必岳麓WE",
    "longitude": 112.946538,
    "latitude": 28.157787
  },
  {
    "id": "1947224199277498407",
    "name": "华美欧大厦",
    "longitude": 112.989361,
    "latitude": 28.19428
  },
  {
    "id": "1947224199277498408",
    "name": "湘域中央商务大厦",
    "longitude": 112.998992,
    "latitude": 28.194064
  },
  {
    "id": "1947224199277498409",
    "name": "华坤时代",
    "longitude": 113.028728,
    "latitude": 28.11154
  },
  {
    "id": "1947224199277498410",
    "name": "万博汇云谷",
    "longitude": 112.995061,
    "latitude": 28.156889
  },
  {
    "id": "1947224199277498411",
    "name": "鑫远国际大厦A座",
    "longitude": 112.977807,
    "latitude": 28.111487
  },
  {
    "id": "1947224199273304087",
    "name": "富兴世界金融中心T3T6",
    "longitude": 112.988366,
    "latitude": 28.216574
  },
  {
    "id": "1947224199277498414",
    "name": "湘江财富金融中心CD座",
    "longitude": 112.956344,
    "latitude": 28.242656
  },
  {
    "id": "1947224199277498415",
    "name": "长沙金茂大厦",
    "longitude": 112.881857,
    "latitude": 28.182518
  }
];

export default addressHooksData;