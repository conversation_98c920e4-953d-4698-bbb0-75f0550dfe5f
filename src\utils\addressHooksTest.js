import addressHooksData from '@/net/addressHooks.js';

/**
 * 测试addressHooks.js数据匹配功能
 */
export function testAddressHooksMatching() {
  console.log('=== addressHooks.js 数据匹配测试 ===');
  console.log(`总共有 ${addressHooksData.length} 个楼宇数据`);
  
  // 测试精确匹配
  const testNames = [
    '长沙国金中心',
    '华远国际中心', 
    '佳兆业广场',
    '德思勤城市广场',
    '万达广场'
  ];
  
  console.log('\n精确匹配测试:');
  testNames.forEach(name => {
    const match = addressHooksData.find(item => item.name === name);
    if (match) {
      console.log(`✓ ${name} -> [${match.longitude}, ${match.latitude}]`);
    } else {
      console.log(`✗ ${name} -> 未找到`);
    }
  });
  
  // 测试模糊匹配
  const fuzzyTestNames = [
    '国金中心',
    '华远国际',
    '佳兆业',
    '德思勤',
    '万达'
  ];
  
  console.log('\n模糊匹配测试:');
  fuzzyTestNames.forEach(name => {
    const match = addressHooksData.find(item => 
      item.name.includes(name) || name.includes(item.name)
    );
    if (match) {
      console.log(`✓ ${name} -> ${match.name} [${match.longitude}, ${match.latitude}]`);
    } else {
      console.log(`✗ ${name} -> 未找到`);
    }
  });
  
  // 统计坐标范围
  const longitudes = addressHooksData.map(item => item.longitude);
  const latitudes = addressHooksData.map(item => item.latitude);
  
  console.log('\n坐标范围统计:');
  console.log(`经度范围: ${Math.min(...longitudes)} ~ ${Math.max(...longitudes)}`);
  console.log(`纬度范围: ${Math.min(...latitudes)} ~ ${Math.max(...latitudes)}`);
  
  return {
    totalCount: addressHooksData.length,
    longitudeRange: [Math.min(...longitudes), Math.max(...longitudes)],
    latitudeRange: [Math.min(...latitudes), Math.max(...latitudes)]
  };
}

/**
 * 根据楼宇名称获取坐标（与DistrictMap中的逻辑一致）
 */
export function getCoordinatesByName(buildingName) {
  if (!buildingName) {
    return null;
  }
  
  // 精确匹配
  const exactMatch = addressHooksData.find(item => item.name === buildingName);
  if (exactMatch) {
    return {
      coordinates: [exactMatch.longitude, exactMatch.latitude],
      matchType: 'exact',
      matchedName: exactMatch.name
    };
  }
  
  // 模糊匹配
  const fuzzyMatch = addressHooksData.find(item => 
    item.name.includes(buildingName) || buildingName.includes(item.name)
  );
  
  if (fuzzyMatch) {
    return {
      coordinates: [fuzzyMatch.longitude, fuzzyMatch.latitude],
      matchType: 'fuzzy',
      matchedName: fuzzyMatch.name
    };
  }
  
  return null;
}

/**
 * 增强的模糊匹配函数 (与DistrictMap中的逻辑一致)
 */
export function getCoordinatesByEnhancedMatching(buildingName) {
  if (!buildingName) {
    return null;
  }

  // 清理楼宇名称，去除常见的后缀和前缀
  const cleanBuildingName = (name) => {
    return name
      .replace(/^长沙/, '') // 去除"长沙"前缀
      .replace(/写字楼$|大厦$|大楼$|中心$|广场$|国际$|商务楼$|办公楼$/, '') // 去除常见后缀
      .replace(/\s+/g, '') // 去除空格
      .trim();
  };

  // 提取关键词的辅助函数
  const extractKeywords = (name) => {
    const keywords = [];

    // 提取数字+字母组合 (如T1, A座, ICC等)
    const alphaNumeric = name.match(/[A-Z]+\d+|[A-Z]+座|\d+[A-Z]+|ICC|IEC/g);
    if (alphaNumeric) {
      keywords.push(...alphaNumeric);
    }

    // 提取中文关键词 (长度>=2的中文词)
    const chineseWords = name.match(/[\u4e00-\u9fa5]{2,}/g);
    if (chineseWords) {
      keywords.push(...chineseWords);
    }

    // 提取英文单词
    const englishWords = name.match(/[a-zA-Z]{2,}/g);
    if (englishWords) {
      keywords.push(...englishWords);
    }

    return keywords.filter(keyword => keyword.length >= 2);
  };

  const cleanInputName = cleanBuildingName(buildingName);

  // 1. 精确匹配
  const exactMatch = addressHooksData.find(item => item.name === buildingName);
  if (exactMatch) {
    return {
      coordinates: [exactMatch.longitude, exactMatch.latitude],
      matchType: 'exact',
      matchedName: exactMatch.name,
      score: 100
    };
  }

  // 2. 增强的模糊匹配
  let bestMatch = null;
  let bestScore = 0;

  addressHooksData.forEach(item => {
    const cleanDataName = cleanBuildingName(item.name);

    // 计算匹配分数
    let score = 0;

    // 完全包含关系 (最高分)
    if (item.name.includes(buildingName) || buildingName.includes(item.name)) {
      score = 100;
    }
    // 清理后的名称包含关系
    else if (cleanDataName.includes(cleanInputName) || cleanInputName.includes(cleanDataName)) {
      score = 90;
    }
    // 关键词匹配
    else {
      const inputKeywords = extractKeywords(cleanInputName);
      const dataKeywords = extractKeywords(cleanDataName);

      const matchedKeywords = inputKeywords.filter(keyword =>
        dataKeywords.some(dataKeyword =>
          dataKeyword.includes(keyword) || keyword.includes(dataKeyword)
        )
      );

      if (matchedKeywords.length > 0) {
        score = (matchedKeywords.length / Math.max(inputKeywords.length, dataKeywords.length)) * 80;
      }
    }

    // 更新最佳匹配
    if (score > bestScore && score >= 60) {
      bestScore = score;
      bestMatch = item;
    }
  });

  if (bestMatch) {
    return {
      coordinates: [bestMatch.longitude, bestMatch.latitude],
      matchType: 'fuzzy',
      matchedName: bestMatch.name,
      score: bestScore
    };
  }

  return null;
}

/**
 * 批量测试楼宇名称匹配
 */
export function batchTestMatching(buildingNames) {
  console.log('\n=== 批量匹配测试 ===');

  const results = {
    total: buildingNames.length,
    exactMatches: 0,
    fuzzyMatches: 0,
    noMatches: 0,
    details: []
  };

  buildingNames.forEach(name => {
    const result = getCoordinatesByEnhancedMatching(name);

    if (result) {
      if (result.matchType === 'exact') {
        results.exactMatches++;
      } else {
        results.fuzzyMatches++;
      }

      results.details.push({
        input: name,
        matched: result.matchedName,
        coordinates: result.coordinates,
        type: result.matchType,
        score: result.score
      });

      console.log(`${result.matchType === 'exact' ? '✓' : '~'} ${name} -> ${result.matchedName} (${result.score}%)`);
    } else {
      results.noMatches++;
      results.details.push({
        input: name,
        matched: null,
        coordinates: null,
        type: 'none',
        score: 0
      });

      console.log(`✗ ${name} -> 未匹配`);
    }
  });

  console.log(`\n匹配统计: 精确${results.exactMatches} | 模糊${results.fuzzyMatches} | 未匹配${results.noMatches} | 总计${results.total}`);

  return results;
}

/**
 * 测试常见的楼宇名称变体
 */
export function testCommonVariants() {
  console.log('\n=== 常见楼宇名称变体测试 ===');

  const testCases = [
    // 原名 -> 变体
    { original: '长沙国金中心', variants: ['国金中心', '国金', 'IFS'] },
    { original: '华远国际中心', variants: ['华远国际', '华远中心', '华远'] },
    { original: '德思勤城市广场', variants: ['德思勤', '德思勤广场', '城市广场'] },
    { original: '长沙佳兆业广场', variants: ['佳兆业广场', '佳兆业', '佳兆业中心'] },
    { original: '富兴世界金融中心T2', variants: ['富兴T2', '富兴金融中心', '富兴世界'] },
    { original: '湘江财富金融中心A座', variants: ['湘江财富A座', '湘江财富', '财富中心'] }
  ];

  testCases.forEach(testCase => {
    console.log(`\n测试原名: ${testCase.original}`);

    testCase.variants.forEach(variant => {
      const result = getCoordinatesByEnhancedMatching(variant);
      if (result) {
        console.log(`  ✓ ${variant} -> ${result.matchedName} (${result.score}%)`);
      } else {
        console.log(`  ✗ ${variant} -> 未匹配`);
      }
    });
  });
}

// 导出测试函数
export default {
  testAddressHooksMatching,
  getCoordinatesByName,
  getCoordinatesByEnhancedMatching,
  batchTestMatching,
  testCommonVariants
};
